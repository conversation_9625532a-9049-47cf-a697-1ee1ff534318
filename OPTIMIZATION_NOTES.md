# 全局替换降档算法优化

## 问题分析

根据日志显示，原来的全局替换算法存在以下问题：

1. **效率低下**：逐步迭代替换，每次只替换一个最高价值物品，需要10次迭代
2. **策略不够优化**：每次只选择第一个比当前最高价值低的替换选项
3. **没有利用已知信息**：明明知道需要减少多少价值（50150），却没有直接寻找最接近目标的组合

## 优化方案

### 1. 新增数据结构

```go
// ReplacementOption 替换选项
type ReplacementOption struct {
    OriginalElixir int          // 原仙丹索引
    NewResult      ElixirResult // 新的仙丹结果
    ValueReduction int64        // 替换后减少的价值
}
```

### 2. 优化的算法流程

#### 2.1 主流程 (globalReplacement)
- 计算当前总价值和需要减少的价值
- 如果已满足奖池限制，直接返回
- 调用优化的替换算法

#### 2.2 优化替换算法 (optimizedGlobalReplacement)
- 获取当前使用的仙丹列表
- 寻找最优的替换组合
- 如果找不到最优组合，回退到原来的逐步替换

#### 2.3 寻找最优替换 (findOptimalReplacement)
- 为每个使用的仙丹生成可能的替换选项
- 只考虑价值更低且未使用的仙丹
- 按价值减少量排序

#### 2.4 最佳组合选择 (findBestCombination)
- **单个替换**：优先寻找单个替换就能满足需求的选项
- **双重替换**：如果单个不够，尝试双重替换组合
- **精确匹配**：选择最接近目标减少量的组合

### 3. 关键优化点

#### 3.1 目标导向
- 明确知道需要减少的价值量
- 直接寻找能达到目标的组合
- 避免过度降档

#### 3.2 组合优化
- 不再逐步替换，而是寻找最优组合
- 支持单个和双重替换
- 选择最接近目标的方案

#### 3.3 效率提升
- 减少迭代次数
- 避免不必要的计算
- 更精确的价值控制

## 算法对比

### 原算法
```
当前价值: 59780, 需要减少: 50150
迭代1: 替换最高价值物品 -> 价值: 56840
迭代2: 替换最高价值物品 -> 价值: 44130
...
迭代10: 最终价值: 19670
```

### 优化算法
```
当前价值: 59780, 需要减少: 50150
分析所有可能的替换组合
找到最接近目标的组合: 单次替换或双重替换
直接达到目标价值: ~9630
```

## 预期效果

1. **减少迭代次数**：从10次迭代减少到1-2次操作
2. **更精确的价值控制**：避免过度降档
3. **更好的用户体验**：减少不必要的价值损失
4. **提高算法效率**：减少计算时间

## 回退机制

如果优化算法找不到合适的组合，会自动回退到原来的逐步替换算法，确保系统的稳定性。

## 测试建议

1. 使用相同的输入数据测试新旧算法
2. 比较最终价值和迭代次数
3. 验证算法的正确性和稳定性
4. 测试各种边界情况
