package unseal

import (
	"encoding/json"
	"fmt"
	"jackpot/api/core"
	"jackpot/filelog"
	"jackpot/items"
	"jackpot/net"
	"jackpot/users"
	"math/rand/v2"
	"os"
)

// Request 请求体
type Request struct {
	Oid    string  `json:"oid"`    // 批次ID
	UserId string  `json:"uid"`    // 用户ID，如果是全局开奖，此处传固定值 -1
	Map    int64   `json:"map"`    // 0-全部地图 100-火焰山，1000-盘丝洞，10000-狮驼岭
	Counts []int64 `json:"counts"` // 每种物品的数量，全地图时长度为24，否则为8
}

// Response 响应数据
type Response struct {
	Request
	Items   []items.Item `json:"items"`
	Elixirs []int        `json:"elixirs"` // 随机选择的仙丹索引（0-7）：单地图模式4个，全地图模式12个（3个地图各4个）
}

// Handler 处理器
type Handler struct {
	logger  core.Logger
	giftMap map[int64]map[int][]items.Item // 修改为支持不同地图：map[mapId][count][]items.Item
}

// NewHandler 创建处理器
func NewHandler() *Handler {
	return &Handler{
		giftMap: make(map[int64]map[int][]items.Item),
	}
}

// Init 初始化处理器
func (h *Handler) Init(logger *filelog.FileLogger, config any) error {
	h.logger = core.NewLoggerAdapter(logger)

	// 读取仙丹开鼎配置文件
	jsonData, err := os.ReadFile("elixir_unseal.json")
	if err != nil {
		return err
	}

	var giftMapTemp = make(map[string]map[string][]items.ItemConfig)
	err = json.Unmarshal(jsonData, &giftMapTemp)
	if err != nil {
		return err
	}

	// 将配置转换为内部格式
	for mapKey, countMap := range giftMapTemp {
		var mapId int64
		fmt.Sscanf(mapKey, "%d", &mapId)

		if h.giftMap[mapId] == nil {
			h.giftMap[mapId] = make(map[int][]items.Item)
		}

		for countKey, itemConfigs := range countMap {
			var count int
			fmt.Sscanf(countKey, "%d", &count)

			var itemList []items.Item
			for _, itemConfig := range itemConfigs {
				itemList = append(itemList, items.Item(itemConfig))
			}
			h.giftMap[mapId][count] = itemList
		}
	}

	h.logger.Debug("elixir_unseal", "礼品地图配置: %+v", h.giftMap)
	return nil
}

// Validate 验证请求
func (h *Handler) Validate(req Request) error {
	if len(req.UserId) == 0 {
		return fmt.Errorf("无效用户参数")
	}

	if req.Map > 0 {
		switch req.Map {
		case net.COST_TYPE_100, net.COST_TYPE_1000, net.COST_TYPE_10000:
		default:
			return fmt.Errorf("无效地图参数")
		}

		if len(req.Counts) != 8 {
			return fmt.Errorf("无效或缺少counts参数")
		}
	} else if req.Map == 0 {
		if len(req.Counts) != 24 {
			return fmt.Errorf("无效或缺少counts参数")
		}
	} else {
		return fmt.Errorf("无效地图参数")
	}

	return nil
}

// Handle 处理请求
func (h *Handler) Handle(req Request) (int64, net.Response, error) {
	var jackpot int64
	var itemList []items.Item
	var allSelectedElixirs []int

	if req.Map > 0 {
		jackpot, itemList, allSelectedElixirs = h.open(req.Map, req.Counts)
	} else if req.Map == 0 {
		// 分割 req.Counts 为 3 个长度为 8 的切片
		var countSlices [3][]int64
		for i := range 3 {
			start := i * 8
			end := start + 8
			countSlices[i] = req.Counts[start:end]
		}

		// 定义一个包含3个地图枚举的数组
		mapId := [3]int64{net.COST_TYPE_100, net.COST_TYPE_1000, net.COST_TYPE_10000}

		// 定义一个空数组 results，数组类型是 items.Item
		results := make([]items.Item, 0)

		// 分别处理每个切片
		var jackpotResult int64
		for i, countSlice := range countSlices {
			var jp int64
			var items []items.Item
			var selectedElixirs []int
			// if req.UserId == "-1" && i > 1 {
			// 	jp, items = h.openGlobal(mapId[i], countSlice)
			// } else {
			// 	jp, items = h.open(mapId[i], countSlice)
			// }
			jp, items, selectedElixirs = h.open(mapId[i], countSlice)
			jackpotResult += jp
			results = append(results, items...)
			allSelectedElixirs = append(allSelectedElixirs, selectedElixirs...)
		}

		jackpot = jackpotResult
		itemList = results
	}

	responseData := &Response{
		Request: req,
		Items:   itemList,
	}

	// 返回开奖结果：单地图模式4个仙丹，全地图模式12个仙丹（3个地图各4个）
	if len(allSelectedElixirs) > 0 {
		responseData.Elixirs = allSelectedElixirs
	}

	response := net.Response{
		Code: 0,
		Msg:  "",
		Data: responseData,
	}

	return jackpot, response, nil
}

// open 开奖逻辑
func (h *Handler) open(mapId int64, countList []int64) (int64, []items.Item, []int) {
	// 获取全局奖池信息
	global := users.GetGlobalStats(mapId, users.VER_ELIXIR)

	h.logger.Debug("elixir_unseal", "[1] 开始开奖, 地图ID:%d, 数量列表:%+v, 全局奖池:%d", mapId, countList, global.TotalJackpot)

	// 步骤1: 随机选择4个仙丹（0-7，可重复）
	selectedElixirs := make([]int, 4)
	for i := 0; i < 4; i++ {
		selectedElixirs[i] = rand.IntN(8) // 0-7
	}

	h.logger.Debug("elixir_unseal", "[2] 选中的仙丹: %+v", selectedElixirs)

	// 步骤2: 统计每种仙丹的出现次数
	elixirCounts := make(map[int]int)
	for _, elixir := range selectedElixirs {
		elixirCounts[elixir]++
	}

	h.logger.Debug("elixir_unseal", "[3] 仙丹出现次数: %+v", elixirCounts)

	// 步骤3: 为每种出现的仙丹选择礼物
	selectedGifts := make(map[int]items.Item) // key: 仙丹索引, value: 选中的礼物
	for elixir, count := range elixirCounts {
		// 根据地图和出现次数从对应奖池中选择礼物
		mapGiftPool, exists := h.giftMap[mapId]
		if !exists {
			h.logger.Debug("elixir_unseal", "[4] 地图 %d 没有礼品池", mapId)
			continue
		}

		giftPool, exists := mapGiftPool[count]
		if !exists || len(giftPool) == 0 {
			h.logger.Debug("elixir_unseal", "[4] 地图 %d 出现次数 %d 没有礼品池", mapId, count)
			continue
		}

		selectedGift := items.SelectItem("elixir_unseal", giftPool)
		selectedGifts[elixir] = selectedGift

		h.logger.Debug("elixir_unseal", "[4] 仙丹 %d (出现次数 %d) 选中礼品: %s (价值:%d)", elixir, count, selectedGift.Name, selectedGift.Value*selectedGift.Count)
	}

	// 步骤4: 根据countList分配数量并计算总价值
	result := make([]items.Item, len(countList))
	totalValue := int64(0)

	for i, count := range countList {
		if count <= 0 {
			continue
		}

		elixirIndex := i % 8 // 获取对应的仙丹索引

		// 检查该仙丹是否出现过
		if gift, exists := selectedGifts[elixirIndex]; exists {
			// 仙丹出现过，使用选中的礼物
			result[i] = items.Item{
				ID:    gift.ID,
				Name:  gift.Name,
				Value: gift.Value,
				Count: gift.Count,
				Total: count,
			}
			totalValue += gift.Value * gift.Count * count
		} else {
			// 仙丹未出现，使用出现次数0的奖池
			mapGiftPool, exists := h.giftMap[mapId]
			if exists {
				giftPool, exists := mapGiftPool[0]
				if exists && len(giftPool) > 0 {
					selectedGift := items.SelectItem("elixir_unseal", giftPool)
					result[i] = items.Item{
						ID:    selectedGift.ID,
						Name:  selectedGift.Name,
						Value: selectedGift.Value,
						Count: selectedGift.Count,
						Total: count,
					}
					totalValue += selectedGift.Value * selectedGift.Count * count

					h.logger.Debug("elixir_unseal", "[4.5] 仙丹 %d (出现次数 0) 选中礼品: %s (价值:%d)", elixirIndex, selectedGift.Name, selectedGift.Value*selectedGift.Count)
				}
			}
		}
	}

	h.logger.Debug("elixir_unseal", "[5] 奖池检查前总价值: %d, 全局奖池: %d", totalValue, global.TotalJackpot)

	// 步骤5: 检查全局奖池限制，必要时降档
	if totalValue > global.TotalJackpot {
		h.logger.Debug("elixir_unseal", "[6] 总价值超过奖池，需要降档")
		result, totalValue = h.downgradeGifts(mapId, result, global.TotalJackpot, elixirCounts, countList)
	}

	// 更新全局奖池
	global.TotalReturn += totalValue
	global.TotalJackpot -= totalValue
	users.UpdateGobalStats(global)

	h.logger.Debug("elixir_unseal", "[7] 最终总价值: %d, 剩余奖池: %d", totalValue, global.TotalJackpot)

	return global.TotalJackpot, result, selectedElixirs
}

// ElixirResult 仙丹开奖结果
type ElixirResult struct {
	ElixirIndex int        // 仙丹索引 (0-7)
	Count       int        // 出现次数 (0-4)
	Gift        items.Item // 选中的礼物
	FinalValue  int64      // 最终价值 (gift.Value * gift.Count * countList[elixirIndex])
}

// ReplacementOption 替换选项
type ReplacementOption struct {
	OriginalElixir int
	NewResult      ElixirResult
	ValueReduction int64 // 替换后减少的价值
}

// calculateAllPossibleResults 计算所有136个可能的开奖结果
func (h *Handler) calculateAllPossibleResults(mapId int64, countList []int64) []ElixirResult {
	var allResults []ElixirResult

	mapGiftPool, exists := h.giftMap[mapId]
	if !exists {
		h.logger.Debug("elixir_unseal", "[计算] 地图 %d 没有礼品池", mapId)
		return allResults
	}

	// 遍历8个仙丹
	for elixirIndex := 0; elixirIndex < 8; elixirIndex++ {
		// 遍历5个出现次数 (0,1,2,3,4)
		for count := 0; count <= 4; count++ {
			giftPool, exists := mapGiftPool[count]
			if !exists || len(giftPool) == 0 {
				continue
			}

			// 遍历该奖池中的所有礼物
			for _, gift := range giftPool {
				// 计算最终价值
				countListValue := int64(0)
				if elixirIndex < len(countList) {
					countListValue = countList[elixirIndex]
				}
				finalValue := gift.Value * gift.Count * countListValue

				result := ElixirResult{
					ElixirIndex: elixirIndex,
					Count:       count,
					Gift:        gift,
					FinalValue:  finalValue,
				}
				allResults = append(allResults, result)
			}
		}
	}

	// 按价值降序排序
	for i := 0; i < len(allResults)-1; i++ {
		for j := i + 1; j < len(allResults); j++ {
			if allResults[i].FinalValue < allResults[j].FinalValue {
				allResults[i], allResults[j] = allResults[j], allResults[i]
			}
		}
	}

	h.logger.Debug("elixir_unseal", "[计算] 计算出 %d 个可能结果", len(allResults))
	return allResults
}

// downgradeGifts 两步降档机制：先正常降档，再全结果重选
func (h *Handler) downgradeGifts(mapId int64, result []items.Item, maxJackpot int64, elixirCounts map[int]int, countList []int64) ([]items.Item, int64) {
	h.logger.Debug("elixir_unseal", "[6.1] 开始两步降档流程, 最大奖池: %d", maxJackpot)

	// 第一步：尝试正常降档（在同一出现次数内降档）
	normalResult, normalValue := h.normalDowngrade(mapId, result, maxJackpot, elixirCounts)

	// 检查正常降档是否成功
	if normalValue <= maxJackpot {
		h.logger.Debug("elixir_unseal", "[6.1] 正常降档成功, 最终价值: %d", normalValue)
		return normalResult, normalValue
	}

	h.logger.Debug("elixir_unseal", "[6.1] 正常降档失败, 价值: %d, 尝试全局替换", normalValue)

	// 第二步：如果正常降档失败，使用全结果重选
	return h.globalReplacement(mapId, normalResult, maxJackpot, elixirCounts, countList)
}

// normalDowngrade 正常降档：在同一出现次数内选择更低价值的礼物
func (h *Handler) normalDowngrade(mapId int64, result []items.Item, maxJackpot int64, elixirCounts map[int]int) ([]items.Item, int64) {
	h.logger.Debug("elixir_unseal", "[6.2] 开始正常降档流程")

	mapGiftPool, exists := h.giftMap[mapId]
	if !exists {
		h.logger.Debug("elixir_unseal", "[6.2.1] 地图 %d 没有礼品池", mapId)
		return result, 0
	}

	maxIterations := 10 // 防止无限循环
	iteration := 0

	for iteration < maxIterations {
		iteration++

		// 计算当前总价值
		currentTotalValue := int64(0)
		for _, item := range result {
			currentTotalValue += item.Value * item.Count * item.Total
		}

		// 检查是否已经满足奖池限制
		if currentTotalValue <= maxJackpot {
			h.logger.Debug("elixir_unseal", "[6.2.%d] 正常降档目标达成, 最终价值: %d", iteration, currentTotalValue)
			return result, currentTotalValue
		}

		needToSave := currentTotalValue - maxJackpot
		h.logger.Debug("elixir_unseal", "[6.2.%d] 需要节省: %d, 当前价值: %d", iteration, needToSave, currentTotalValue)

		// 创建降档选项列表
		type DowngradeOption struct {
			Index       int        // 结果数组中的索引
			ElixirIndex int        // 仙丹索引
			CurrentGift items.Item // 当前礼物
			NewGift     items.Item // 降档后的礼物
			SavedValue  int64      // 节省的价值
		}

		var options []DowngradeOption

		// 为每个有礼物的位置寻找降档选项（只在同一出现次数内）
		for i, item := range result {
			if item.Value == 0 || item.Total == 0 {
				continue
			}

			elixirIndex := i % 8

			// 确定该位置对应的出现次数
			var currentCount int
			if gift, exists := elixirCounts[elixirIndex]; exists {
				// 仙丹出现过，使用其出现次数
				currentCount = gift
			} else {
				// 仙丹未出现，使用出现次数0
				currentCount = 0
			}

			// 只在当前出现次数的奖池中寻找更低价值的礼物
			currentPool, exists := mapGiftPool[currentCount]
			if !exists || len(currentPool) == 0 {
				continue
			}

			// 寻找同一奖池中价值更低的礼物
			for _, lowerGift := range currentPool {
				if lowerGift.Value*lowerGift.Count < item.Value*item.Count {
					currentValue := item.Value * item.Count * item.Total
					newValue := lowerGift.Value * lowerGift.Count * item.Total
					savedValue := currentValue - newValue

					if savedValue > 0 {
						options = append(options, DowngradeOption{
							Index:       i,
							ElixirIndex: elixirIndex,
							CurrentGift: item,
							NewGift: items.Item{
								ID:    lowerGift.ID,
								Name:  lowerGift.Name,
								Value: lowerGift.Value,
								Count: lowerGift.Count,
								Total: item.Total,
							},
							SavedValue: savedValue,
						})
					}
				}
			}
		}

		if len(options) == 0 {
			h.logger.Debug("elixir_unseal", "[6.2.%d] 没有更多正常降档选项可用", iteration)
			break
		}

		h.logger.Debug("elixir_unseal", "[6.2.%d] 找到 %d 个正常降档选项", iteration, len(options))

		// 按节省价值从小到大排序，优先选择节省幅度最小的（更精确的降档）
		for i := 0; i < len(options)-1; i++ {
			for j := i + 1; j < len(options); j++ {
				if options[i].SavedValue > options[j].SavedValue {
					options[i], options[j] = options[j], options[i]
				}
			}
		}

		// 选择节省价值最小但足够的降档选项
		var selectedOption *DowngradeOption
		for _, option := range options {
			if option.SavedValue >= needToSave {
				selectedOption = &option
				break
			}
		}

		// 如果没有单个选项能满足需求，选择节省最多的
		if selectedOption == nil {
			// 重新按节省价值从大到小排序
			for i := 0; i < len(options)-1; i++ {
				for j := i + 1; j < len(options); j++ {
					if options[i].SavedValue < options[j].SavedValue {
						options[i], options[j] = options[j], options[i]
					}
				}
			}
			selectedOption = &options[0]
		}

		h.logger.Debug("elixir_unseal", "[6.2.%d] 应用正常降档在索引 %d: %s -> %s, 节省: %d",
			iteration, selectedOption.Index, selectedOption.CurrentGift.Name, selectedOption.NewGift.Name,
			selectedOption.SavedValue)

		result[selectedOption.Index] = selectedOption.NewGift
	}

	// 最终计算总价值
	finalTotalValue := int64(0)
	for _, item := range result {
		finalTotalValue += item.Value * item.Count * item.Total
	}

	h.logger.Debug("elixir_unseal", "[6.2.最终] 正常降档完成，经过 %d 次迭代, 最终价值: %d", iteration, finalTotalValue)

	return result, finalTotalValue
}

// globalReplacement 全结果重选：基于全结果价值排序的降档
func (h *Handler) globalReplacement(mapId int64, result []items.Item, maxJackpot int64, elixirCounts map[int]int, countList []int64) ([]items.Item, int64) {
	h.logger.Debug("elixir_unseal", "[6.3] 开始全局替换流程")

	// 计算当前总价值
	currentTotalValue := int64(0)
	for _, item := range result {
		currentTotalValue += item.Value * item.Count * item.Total
	}

	// 如果已经满足奖池限制，直接返回
	if currentTotalValue <= maxJackpot {
		h.logger.Debug("elixir_unseal", "[6.3] 当前价值已满足奖池限制, 价值: %d", currentTotalValue)
		return result, currentTotalValue
	}

	needToReduce := currentTotalValue - maxJackpot
	h.logger.Debug("elixir_unseal", "[6.3] 当前价值: %d, 需要减少: %d", currentTotalValue, needToReduce)

	// 计算所有可能的结果
	allPossibleResults := h.calculateAllPossibleResults(mapId, countList)
	if len(allPossibleResults) == 0 {
		h.logger.Debug("elixir_unseal", "[6.3] 没有计算出可能的结果")
		return result, currentTotalValue
	}

	// 使用优化的组合选择算法
	return h.optimizedGlobalReplacement(result, maxJackpot, elixirCounts, allPossibleResults, needToReduce)
}

// optimizedGlobalReplacement 优化的全局替换算法
func (h *Handler) optimizedGlobalReplacement(result []items.Item, maxJackpot int64, elixirCounts map[int]int, allPossibleResults []ElixirResult, needToReduce int64) ([]items.Item, int64) {
	h.logger.Debug("elixir_unseal", "[6.3.优化] 开始优化的全局替换")

	// 获取当前使用的仙丹索引列表
	usedElixirs := make([]int, 0, len(elixirCounts))
	for elixirIndex := range elixirCounts {
		usedElixirs = append(usedElixirs, elixirIndex)
	}

	// 尝试找到最优的替换组合
	bestResult, bestValue := h.findOptimalReplacement(result, elixirCounts, allPossibleResults, usedElixirs, maxJackpot, needToReduce)

	if bestResult != nil {
		h.logger.Debug("elixir_unseal", "[6.3.优化] 找到最优替换组合, 最终价值: %d", bestValue)
		return bestResult, bestValue
	}

	// 如果找不到最优组合，回退到原来的逐步替换算法
	h.logger.Debug("elixir_unseal", "[6.3.优化] 未找到最优组合，回退到逐步替换")
	return h.iterativeReplacement(result, maxJackpot, elixirCounts, allPossibleResults)
}

// findOptimalReplacement 寻找最优的替换组合
func (h *Handler) findOptimalReplacement(result []items.Item, elixirCounts map[int]int, allPossibleResults []ElixirResult, usedElixirs []int, maxJackpot int64, needToReduce int64) ([]items.Item, int64) {
	// 为每个使用的仙丹生成可能的替换选项
	type ReplacementOption struct {
		OriginalElixir int
		NewResult      ElixirResult
		ValueReduction int64 // 替换后减少的价值
	}

	var allOptions []ReplacementOption

	for _, usedElixir := range usedElixirs {
		// 计算当前仙丹的价值
		currentValue := h.getElixirValue(result, usedElixir)

		// 寻找可以替换的选项（未使用的仙丹且价值更低）
		for _, possibleResult := range allPossibleResults {
			// 跳过已经使用的仙丹
			if h.isElixirUsed(possibleResult.ElixirIndex, elixirCounts) {
				continue
			}

			// 只考虑价值更低的替换
			if possibleResult.FinalValue < currentValue {
				valueReduction := currentValue - possibleResult.FinalValue
				allOptions = append(allOptions, ReplacementOption{
					OriginalElixir: usedElixir,
					NewResult:      possibleResult,
					ValueReduction: valueReduction,
				})
			}
		}
	}

	if len(allOptions) == 0 {
		return nil, 0
	}

	// 按价值减少量排序，优先选择减少量大的
	for i := 0; i < len(allOptions)-1; i++ {
		for j := i + 1; j < len(allOptions); j++ {
			if allOptions[i].ValueReduction < allOptions[j].ValueReduction {
				allOptions[i], allOptions[j] = allOptions[j], allOptions[i]
			}
		}
	}

	// 尝试找到最接近目标减少量的组合
	return h.findBestCombination(result, elixirCounts, allOptions, needToReduce, maxJackpot)
}

// getElixirValue 获取指定仙丹在当前结果中的价值
func (h *Handler) getElixirValue(result []items.Item, elixirIndex int) int64 {
	for i, item := range result {
		if i%8 == elixirIndex && item.Value > 0 && item.Total > 0 {
			return item.Value * item.Count * item.Total
		}
	}
	return 0
}

// isElixirUsed 检查仙丹是否已被使用
func (h *Handler) isElixirUsed(elixirIndex int, elixirCounts map[int]int) bool {
	_, exists := elixirCounts[elixirIndex]
	return exists
}

// findBestCombination 寻找最佳的替换组合
func (h *Handler) findBestCombination(result []items.Item, elixirCounts map[int]int, options []ReplacementOption, needToReduce int64, maxJackpot int64) ([]items.Item, int64) {

	// 尝试单个替换
	for _, option := range options {
		if option.ValueReduction >= needToReduce {
			// 单个替换就能满足需求
			newResult := h.applyReplacement(result, elixirCounts, option.OriginalElixir, option.NewResult)
			newValue := h.calculateTotalValue(newResult)

			if newValue <= maxJackpot {
				h.logger.Debug("elixir_unseal", "[6.3.组合] 单个替换成功: 仙丹 %d -> 仙丹 %d, 减少价值: %d",
					option.OriginalElixir, option.NewResult.ElixirIndex, option.ValueReduction)
				return newResult, newValue
			}
		}
	}

	// 尝试双重替换组合
	for i := 0; i < len(options); i++ {
		for j := i + 1; j < len(options); j++ {
			option1 := options[i]
			option2 := options[j]

			// 确保不会替换同一个仙丹
			if option1.OriginalElixir == option2.OriginalElixir {
				continue
			}

			// 确保新仙丹不冲突
			if option1.NewResult.ElixirIndex == option2.NewResult.ElixirIndex {
				continue
			}

			totalReduction := option1.ValueReduction + option2.ValueReduction
			if totalReduction >= needToReduce {
				// 应用双重替换
				newResult := h.applyDoubleReplacement(result, elixirCounts, option1, option2)
				newValue := h.calculateTotalValue(newResult)

				if newValue <= maxJackpot {
					h.logger.Debug("elixir_unseal", "[6.3.组合] 双重替换成功: 仙丹 %d -> 仙丹 %d, 仙丹 %d -> 仙丹 %d, 减少价值: %d",
						option1.OriginalElixir, option1.NewResult.ElixirIndex,
						option2.OriginalElixir, option2.NewResult.ElixirIndex, totalReduction)
					return newResult, newValue
				}
			}
		}
	}

	// 如果没有找到合适的组合，返回nil
	return nil, 0
}

// applyReplacement 应用单个替换
func (h *Handler) applyReplacement(result []items.Item, elixirCounts map[int]int, originalElixir int, newResult ElixirResult) []items.Item {
	// 复制结果数组
	newResultArray := make([]items.Item, len(result))
	copy(newResultArray, result)

	// 复制仙丹计数
	newElixirCounts := make(map[int]int)
	for k, v := range elixirCounts {
		newElixirCounts[k] = v
	}

	// 移除原仙丹
	delete(newElixirCounts, originalElixir)

	// 清空原仙丹位置
	for i := range newResultArray {
		if i%8 == originalElixir {
			newResultArray[i] = items.Item{}
		}
	}

	// 添加新仙丹
	newElixirCounts[newResult.ElixirIndex] = newResult.Count

	// 设置新仙丹位置
	for i := range newResultArray {
		if i%8 == newResult.ElixirIndex {
			newResultArray[i] = items.Item{
				ID:    newResult.Gift.ID,
				Name:  newResult.Gift.Name,
				Value: newResult.Gift.Value,
				Count: newResult.Gift.Count,
				Total: newResult.Gift.Total,
			}
		}
	}

	return newResultArray
}

// applyDoubleReplacement 应用双重替换
func (h *Handler) applyDoubleReplacement(result []items.Item, elixirCounts map[int]int, option1, option2 ReplacementOption) []items.Item {
	// 先应用第一个替换
	tempResult := h.applyReplacement(result, elixirCounts, option1.OriginalElixir, option1.NewResult)

	// 更新仙丹计数
	newElixirCounts := make(map[int]int)
	for k, v := range elixirCounts {
		newElixirCounts[k] = v
	}
	delete(newElixirCounts, option1.OriginalElixir)
	newElixirCounts[option1.NewResult.ElixirIndex] = option1.NewResult.Count

	// 再应用第二个替换
	return h.applyReplacement(tempResult, newElixirCounts, option2.OriginalElixir, option2.NewResult)
}

// calculateTotalValue 计算总价值
func (h *Handler) calculateTotalValue(result []items.Item) int64 {
	totalValue := int64(0)
	for _, item := range result {
		totalValue += item.Value * item.Count * item.Total
	}
	return totalValue
}

// iterativeReplacement 迭代替换算法（原来的逐步替换逻辑）
func (h *Handler) iterativeReplacement(result []items.Item, maxJackpot int64, elixirCounts map[int]int, allPossibleResults []ElixirResult) ([]items.Item, int64) {
	h.logger.Debug("elixir_unseal", "[6.3.迭代] 开始迭代替换流程")

	maxIterations := 10 // 防止无限循环
	iteration := 0

	for iteration < maxIterations {
		iteration++

		// 计算当前总价值
		currentTotalValue := h.calculateTotalValue(result)

		// 检查是否已经满足奖池限制
		if currentTotalValue <= maxJackpot {
			h.logger.Debug("elixir_unseal", "[6.3.迭代.%d] 目标达成, 最终价值: %d", iteration, currentTotalValue)
			return result, currentTotalValue
		}

		h.logger.Debug("elixir_unseal", "[6.3.迭代.%d] 当前价值: %d, 需要减少: %d", iteration, currentTotalValue, currentTotalValue-maxJackpot)

		// 找到当前价值最高的仙丹位置
		maxValueIndex := -1
		maxValue := int64(0)
		for i, item := range result {
			if item.Value > 0 && item.Total > 0 {
				itemValue := item.Value * item.Count * item.Total
				if itemValue > maxValue {
					maxValue = itemValue
					maxValueIndex = i
				}
			}
		}

		if maxValueIndex == -1 {
			h.logger.Debug("elixir_unseal", "[6.3.迭代.%d] 没有找到有效的替换物品", iteration)
			break
		}

		currentMaxItem := result[maxValueIndex]
		currentMaxElixirIndex := maxValueIndex % 8

		h.logger.Debug("elixir_unseal", "[6.3.迭代.%d] 最高价值物品在索引 %d (仙丹 %d): %s, 价值: %d",
			iteration, maxValueIndex, currentMaxElixirIndex, currentMaxItem.Name, maxValue)

		// 从所有可能结果中找到比当前最高价值低的结果，但不能是已经在当前开奖中的仙丹
		var replacementResult *ElixirResult
		for _, possibleResult := range allPossibleResults {
			// 跳过价值不低于当前最高价值的结果
			if possibleResult.FinalValue >= maxValue {
				continue
			}

			// 检查这个仙丹是否已经在当前开奖结果中
			if h.isElixirUsed(possibleResult.ElixirIndex, elixirCounts) {
				continue
			}

			// 找到第一个合适的替换选项
			replacementResult = &possibleResult
			break
		}

		if replacementResult == nil {
			h.logger.Debug("elixir_unseal", "[6.3.迭代.%d] 没有找到合适的替换", iteration)
			break
		}

		h.logger.Debug("elixir_unseal", "[6.3.迭代.%d] 找到替换: 仙丹 %d, 出现次数 %d, 礼品: %s, 价值: %d",
			iteration, replacementResult.ElixirIndex, replacementResult.Count,
			replacementResult.Gift.Name, replacementResult.FinalValue)

		// 执行替换
		result = h.applyReplacement(result, elixirCounts, currentMaxElixirIndex, *replacementResult)

		// 更新仙丹计数
		delete(elixirCounts, currentMaxElixirIndex)
		elixirCounts[replacementResult.ElixirIndex] = replacementResult.Count

		h.logger.Debug("elixir_unseal", "[6.3.迭代.%d] 替换完成", iteration)
	}

	// 最终计算总价值
	finalTotalValue := h.calculateTotalValue(result)
	h.logger.Debug("elixir_unseal", "[6.3.迭代.最终] 迭代替换完成，经过 %d 次迭代, 最终价值: %d", iteration, finalTotalValue)

	return result, finalTotalValue
}

// NewModule 创建模块
func NewModule() core.APIModule {
	handler := NewHandler()

	return core.NewModuleBuilder[Request, net.Response]("elixir_unseal", "v1", net.MSG_TYPE_ELIXIR_UNSEAL).
		WithHandler(handler).
		Build()
}
