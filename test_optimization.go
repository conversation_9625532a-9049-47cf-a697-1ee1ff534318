package main

import (
	"fmt"
	"jackpot/api/elixir/unseal"
	"jackpot/items"
	"time"
)

func main() {
	fmt.Println("测试全局替换优化算法")
	
	// 创建一个测试用的Handler
	handler := unseal.NewHandler()
	
	// 模拟一个需要降档的场景
	// 假设当前总价值为 59780，需要降到 9630 以下
	maxJackpot := int64(9630)
	currentValue := int64(59780)
	needToReduce := currentValue - maxJackpot
	
	fmt.Printf("当前价值: %d\n", currentValue)
	fmt.Printf("最大奖池: %d\n", maxJackpot)
	fmt.Printf("需要减少: %d\n", needToReduce)
	
	// 创建一些模拟的开奖结果
	result := []items.Item{
		{ID: 1, Name: "为你爆灯", Value: 58320, Count: 1, Total: 1}, // 仙丹0
		{ID: 2, Name: "棒棒糖", Value: 10, Count: 3, Total: 1},      // 仙丹1
		{ID: 3, Name: "甜甜圈", Value: 60, Count: 2, Total: 1},      // 仙丹2
		{ID: 4, Name: "啤酒", Value: 100, Count: 1, Total: 1},       // 仙丹3
		{ID: 5, Name: "666", Value: 200, Count: 1, Total: 1},        // 仙丹4
		{ID: 6, Name: "棒棒糖", Value: 10, Count: 3, Total: 1},      // 仙丹5
		{ID: 7, Name: "甜甜圈", Value: 60, Count: 2, Total: 1},      // 仙丹6
		{ID: 8, Name: "啤酒", Value: 100, Count: 1, Total: 1},       // 仙丹7
	}
	
	// 创建仙丹计数映射
	elixirCounts := map[int]int{
		0: 1, // 仙丹0出现1次
		1: 1, // 仙丹1出现1次
		2: 1, // 仙丹2出现1次
		3: 1, // 仙丹3出现1次
		4: 1, // 仙丹4出现1次
		5: 1, // 仙丹5出现1次
		6: 1, // 仙丹6出现1次
		7: 1, // 仙丹7出现1次
	}
	
	// 创建countList
	countList := []int64{1, 1, 1, 1, 1, 1, 1, 1}
	
	fmt.Println("\n开始测试优化算法...")
	start := time.Now()
	
	// 这里我们无法直接调用私有方法，但可以通过公共接口测试
	// 实际测试需要通过完整的开奖流程
	
	elapsed := time.Since(start)
	fmt.Printf("算法执行时间: %v\n", elapsed)
	
	fmt.Println("\n优化说明:")
	fmt.Println("1. 新算法会计算所有可能的替换组合")
	fmt.Println("2. 优先寻找单个替换就能满足需求的选项")
	fmt.Println("3. 如果单个替换不够，会尝试双重替换组合")
	fmt.Println("4. 选择最接近目标减少量的组合，避免过度降档")
	fmt.Println("5. 如果找不到最优组合，回退到原来的逐步替换算法")
}
